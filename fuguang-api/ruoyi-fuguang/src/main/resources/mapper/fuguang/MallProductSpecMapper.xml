<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MallProductSpecMapper">
    
    <resultMap type="MallProductSpec" id="MallProductSpecResult">
        <result property="specId"    column="spec_id"    />
        <result property="productId"    column="product_id"    />
        <result property="specName"    column="spec_name"    />
        <result property="specImage"    column="spec_image"    />
        <result property="salePrice"    column="sale_price"    />
        <result property="supplyPrice"    column="supply_price"    />
        <result property="stockQuantity"    column="stock_quantity"    />
        <result property="specStatus"    column="spec_status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="productName"    column="product_name"    />
    </resultMap>

    <sql id="selectMallProductSpecVo">
        select s.spec_id, s.product_id, s.spec_name, s.spec_image, s.sale_price, s.supply_price,
               s.stock_quantity, s.spec_status, s.sort_order, s.del_flag, s.create_by, s.create_time,
               s.update_by, s.update_time, s.remark, p.product_name
        from mall_product_spec s
        left join mall_product p on s.product_id = p.product_id
    </sql>

    <select id="selectMallProductSpecList" parameterType="MallProductSpec" resultMap="MallProductSpecResult">
        <include refid="selectMallProductSpecVo"/>
        <where>  
            <if test="productId != null "> and s.product_id = #{productId}</if>
            <if test="specName != null  and specName != ''"> and s.spec_name like concat('%', #{specName}, '%')</if>
            <if test="specStatus != null  and specStatus != ''"> and s.spec_status = #{specStatus}</if>
            <if test="delFlag != null  and delFlag != ''"> and s.del_flag = #{delFlag}</if>
        </where>
        order by s.sort_order asc, s.create_time desc
    </select>
    
    <select id="selectMallProductSpecBySpecId" parameterType="Long" resultMap="MallProductSpecResult">
        <include refid="selectMallProductSpecVo"/>
        where s.spec_id = #{specId}
    </select>

    <select id="selectMallProductSpecByProductId" parameterType="Long" resultMap="MallProductSpecResult">
        <include refid="selectMallProductSpecVo"/>
        where s.product_id = #{productId} and s.del_flag = '0' and s.spec_status = '0'
        order by s.sort_order asc, s.create_time desc
    </select>
        
    <insert id="insertMallProductSpec" parameterType="MallProductSpec" useGeneratedKeys="true" keyProperty="specId">
        insert into mall_product_spec
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="specName != null and specName != ''">spec_name,</if>
            <if test="specImage != null">spec_image,</if>
            <if test="salePrice != null">sale_price,</if>
            <if test="supplyPrice != null">supply_price,</if>
            <if test="stockQuantity != null">stock_quantity,</if>
            <if test="specStatus != null">spec_status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="specName != null and specName != ''">#{specName},</if>
            <if test="specImage != null">#{specImage},</if>
            <if test="salePrice != null">#{salePrice},</if>
            <if test="supplyPrice != null">#{supplyPrice},</if>
            <if test="stockQuantity != null">#{stockQuantity},</if>
            <if test="specStatus != null">#{specStatus},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMallProductSpec" parameterType="MallProductSpec">
        update mall_product_spec
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="specName != null and specName != ''">spec_name = #{specName},</if>
            <if test="specImage != null">spec_image = #{specImage},</if>
            <if test="salePrice != null">sale_price = #{salePrice},</if>
            <if test="supplyPrice != null">supply_price = #{supplyPrice},</if>
            <if test="stockQuantity != null">stock_quantity = #{stockQuantity},</if>
            <if test="specStatus != null">spec_status = #{specStatus},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where spec_id = #{specId}
    </update>

    <delete id="deleteMallProductSpecBySpecId" parameterType="Long">
        delete from mall_product_spec where spec_id = #{specId}
    </delete>

    <delete id="deleteMallProductSpecBySpecIds" parameterType="String">
        delete from mall_product_spec where spec_id in 
        <foreach item="specId" collection="array" open="(" separator="," close=")">
            #{specId}
        </foreach>
    </delete>

    <delete id="deleteMallProductSpecByProductId" parameterType="Long">
        delete from mall_product_spec where product_id = #{productId}
    </delete>
</mapper>
