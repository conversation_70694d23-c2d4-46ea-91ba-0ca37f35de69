# 商品规格和物流功能开发说明

## 功能概述

本次开发为商城系统添加了以下核心功能：

1. **商品规格管理** - 支持一个商品对应多个规格，每个规格有独立的价格、库存、图片等信息
2. **订单地址关联** - 订单与用户地址表关联，便于地址信息管理
3. **物流信息管理** - 支持发货、退货等物流信息的跟踪和管理

## 数据库变更

### 新增表

1. **mall_product_spec** - 商品规格表
   - 存储商品规格信息：规格名称、售卖价格、供货价、库存、图片等
   - 支持规格的上下架状态管理

2. **mall_order_logistics** - 订单物流表
   - 记录发货、退货等物流信息
   - 支持物流状态跟踪和物流信息更新

### 修改表

1. **mall_cart** - 购物车表
   - 添加 `spec_id` 字段，支持规格选择
   - 更新唯一索引，支持同商品不同规格

2. **mall_order** - 订单表
   - 添加 `address_id` 字段，关联用户地址

3. **mall_order_item** - 订单详情表
   - 添加规格相关字段：`spec_id`、`spec_name`、`spec_image`

## 代码结构

### 实体类 (Domain)
- `MallProductSpec` - 商品规格实体
- `MallOrderLogistics` - 订单物流实体
- 更新了 `MallCart`、`MallOrder`、`MallOrderItem` 实体

### 数据访问层 (Mapper)
- `MallProductSpecMapper` - 商品规格数据访问
- `MallOrderLogisticsMapper` - 物流信息数据访问
- 更新了相关 Mapper XML 文件

### 业务逻辑层 (Service)
- `IMallProductSpecService` / `MallProductSpecServiceImpl` - 商品规格业务逻辑
- `IMallOrderLogisticsService` / `MallOrderLogisticsServiceImpl` - 物流信息业务逻辑

### 控制器层 (Controller)
- `MallProductSpecController` - 商品规格管理接口
- `MallOrderLogisticsController` - 物流信息管理接口

## 部署步骤

1. **执行数据库升级脚本**
   ```sql
   -- 执行 fuguang-api/sql/mall_upgrade.sql
   ```

2. **重启应用服务**
   - 确保新的实体类和接口被正确加载

3. **验证菜单权限**
   - 登录管理后台，检查"商品规格"和"物流管理"菜单是否正常显示

## 功能测试

### 商品规格管理测试
1. 进入"商城管理" -> "商品规格"
2. 为现有商品添加规格信息
3. 测试规格的增删改查功能
4. 验证规格状态管理（上架/下架）

### 购物车规格选择测试
1. 前端购物车添加商品时选择规格
2. 验证同商品不同规格可以分别加入购物车
3. 测试购物车数量统计和价格计算

### 订单地址关联测试
1. 创建订单时选择收货地址
2. 验证订单与地址的关联关系
3. 测试地址信息在订单中的正确显示

### 物流信息管理测试
1. 进入"商城管理" -> "物流管理"
2. 为已付款订单创建发货记录
3. 测试物流状态更新功能
4. 验证物流跟踪信息的记录和查询

## API 接口

### 商品规格相关接口
- `GET /mall/spec/list` - 查询规格列表
- `GET /mall/spec/listByProduct/{productId}` - 根据商品ID查询规格
- `POST /mall/spec` - 新增规格
- `PUT /mall/spec` - 修改规格
- `DELETE /mall/spec/{specIds}` - 删除规格

### 物流信息相关接口
- `GET /mall/logistics/list` - 查询物流列表
- `GET /mall/logistics/listByOrder/{orderId}` - 根据订单ID查询物流
- `POST /mall/logistics` - 新增物流记录
- `POST /mall/logistics/delivery` - 创建发货记录
- `PUT /mall/logistics/status` - 更新物流状态

## 注意事项

1. **数据兼容性** - 现有订单和购物车数据不受影响，新字段允许为空
2. **规格价格优先级** - 如果商品有规格，优先使用规格价格；否则使用商品价格
3. **库存管理** - 规格有独立库存，需要在业务逻辑中正确处理
4. **权限控制** - 新增的管理功能需要相应的权限才能访问

## 后续优化建议

1. **前端界面** - 开发对应的前端管理界面
2. **库存同步** - 实现商品总库存与规格库存的同步机制
3. **物流对接** - 集成第三方物流API，实现自动物流信息更新
4. **数据统计** - 添加规格销量统计和物流效率分析功能
