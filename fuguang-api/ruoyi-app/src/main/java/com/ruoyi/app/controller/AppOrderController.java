package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.fuguang.domain.MallOrder;
import com.ruoyi.fuguang.service.IMallOrderService;
import com.ruoyi.fuguang.service.IMallPaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * APP订单接口
 * 
 * <AUTHOR>
 */
@Api(tags = "APP订单接口")
@RestController("appOrderController")
@RequestMapping("/app/order")
public class AppOrderController extends BaseController
{
    @Autowired
    private IMallOrderService mallOrderService;


    @Autowired
    private IMallPaymentService mallPaymentService;

    /**
     * 获取订单列表
     */
    @ApiOperation("获取订单列表")
    @GetMapping("/list")
    public TableDataInfo getOrderList(
            @ApiParam("订单状态") @RequestParam(required = false) String orderStatus,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize)
    {
        Long userId = getUserId();
        startPage();
        List<MallOrder> list = mallOrderService.selectOrderListByUserId(userId, orderStatus);
        return getDataTable(list);
    }

    /**
     * 获取订单详情
     */
    @ApiOperation("获取订单详情")
    @GetMapping("/{orderId}")
    public AjaxResult getOrderDetail(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!order.getUserId().equals(userId)) {
            return error("无权访问此订单");
        }
        
        return success(order);
    }

    /**
     * 创建订单
     */
    @ApiOperation("创建订单")
    @PostMapping("/create")
    public AjaxResult createOrder(@RequestBody MallOrder mallOrder)
    {
        try {
            MallOrder order = mallOrderService.createOrder(mallOrder);
            return success(order);
        } catch (Exception e) {
            return error("订单创建失败：" + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @ApiOperation("取消订单")
    @PutMapping("/{orderId}/cancel")
    public AjaxResult cancelOrder(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        int result = mallOrderService.cancelOrder(orderId, userId);
        
        if (result > 0) {
            return success("订单取消成功");
        } else {
            return error("订单取消失败");
        }
    }

    /**
     * 确认收货
     */
    @ApiOperation("确认收货")
    @PutMapping("/{orderId}/confirm")
    public AjaxResult confirmReceive(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        int result = mallOrderService.confirmReceive(orderId, userId);
        
        if (result > 0) {
            return success("确认收货成功");
        } else {
            return error("确认收货失败");
        }
    }

    /**
     * 创建支付订单
     */
    @ApiOperation("创建支付订单")
    @PostMapping("/{orderId}/pay")
    public AjaxResult createPayment(
            @ApiParam("订单ID") @PathVariable Long orderId,
            @ApiParam("支付方式") @RequestParam(defaultValue = "1") String payType)
    {
        Long userId = getUserId();
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!order.getUserId().equals(userId)) {
            return error("无权访问此订单");
        }
        
        if (!"0".equals(order.getOrderStatus())) {
            return error("订单状态不正确");
        }
        
        try {
            if ("1".equals(payType)) {
                // 支付宝支付
                Map<String, Object> payData = mallPaymentService.createAlipayOrder(order);
                return success("支付订单创建成功", payData);
            } else {
                return error("暂不支持该支付方式");
            }
        } catch (Exception e) {
            return error("支付订单创建失败：" + e.getMessage());
        }
    }

    /**
     * 查询支付状态
     */
    @ApiOperation("查询支付状态")
    @GetMapping("/{orderId}/pay/status")
    public AjaxResult getPaymentStatus(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!order.getUserId().equals(userId)) {
            return error("无权访问此订单");
        }
        
        String payStatus = mallPaymentService.getPaymentStatus(order.getOrderNo());
        
        Map<String, Object> data = new HashMap<>();
        data.put("payStatus", payStatus);
        data.put("orderStatus", order.getOrderStatus());
        
        return success(data);
    }

    /**
     * 获取用户订单统计
     */
    @ApiOperation("获取用户订单统计")
    @GetMapping("/stats")
    public AjaxResult getUserOrderStats()
    {
        Long userId = getUserId();
        Object stats = mallOrderService.getUserOrderStats(userId);
        return success(stats);
    }
}
